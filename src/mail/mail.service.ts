import { Injectable, Logger } from '@nestjs/common';
import { EmailTemplateService } from './email-template.service';
import { ShopifyOrder } from '../purchases/interfaces/shopify.interface';
const Mailjet = require('node-mailjet');

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);
  private mailjet: any;

  constructor(private readonly emailTemplateService: EmailTemplateService) {
    const apiKey: string = process.env.MJ_KEY || '';
    const apiSecret: string = process.env.MJ_SECRET || '';
    this.mailjet = Mailjet.apiConnect(apiKey, apiSecret);
  }

  getMailJet(): any {
    return this.mailjet;
  }

  async sendMail(
    email: string,
    name: string,
    subject: string,
    text: string,
    html: string,
    inlinedAttachments?: Array<{
      ContentID: string;
      ContentType: string;
      Base64Content: string;
      Filename?: string;
    }>,
  ) {
    const message = {
      From: {
        Email: '<EMAIL>',
        Name: 'IMVI',
      },
      To: [
        {
          Email: email,
          Name: name,
        },
      ],
      Subject: subject,
      TextPart: text,
      HTMLPart: html,
    };

    if (inlinedAttachments) {
      message['InlinedAttachments'] = inlinedAttachments;
    }
    const Messages = [message];

    // Temporary debug logging to diagnose HTML email issue
    console.log('[DEBUG] Email being sent to Mailjet:');
    console.log(`  HTML length: ${html?.length || 0}`);
    console.log(`  Text length: ${text?.length || 0}`);
    console.log(
      `  HTML has styles: ${html?.includes('font-family') ? 'YES' : 'NO'}`,
    );
    console.log(`  HTML has content: ${html?.includes('<div') ? 'YES' : 'NO'}`);

    try {
      const request = await this.mailjet
        .post('send', { version: 'v3.1' })
        .request({
          Messages: Messages,
        });

      const result = await request;
      console.log(result.body);
      return result.body;
    } catch (error) {
      console.log('Error sending email:', error);
      throw new Error('Mail sending failed');
    }
  }

  /**
   * Send activation email to customer with their activation code
   */
  async sendActivationEmail(
    email: string,
    customerName: string,
    activationCode: string,
    shopifyOrder: ShopifyOrder,
  ): Promise<void> {
    // Detect language from billing address country
    const language =
      this.emailTemplateService.detectLanguageFromOrder(shopifyOrder);

    // Additional debugging for language detection
    this.logger.debug(
      `[Email Debug] Order billing country: ${shopifyOrder.billing_address?.country}`,
    );
    this.logger.debug(
      `[Email Debug] Order shipping country: ${shopifyOrder.shipping_address?.country}`,
    );
    this.logger.debug(`[Email Debug] Detected language: ${language}`);

    // Prepare template variables
    const templateVariables = {
      customerName,
      activationCode,
      orderNumber:
        shopifyOrder.order_number || shopifyOrder.id?.toString() || '',
      currency: shopifyOrder.currency || 'USD',
      totalPrice: shopifyOrder.total_price || '0.00',
      email,
    };

    // Get localized email template
    const emailTemplate = this.emailTemplateService.getActivationEmailTemplate(
      language,
      templateVariables,
    );

    // Send the email using the localized template
    await this.sendMail(
      email,
      customerName,
      emailTemplate.subject,
      emailTemplate.text,
      emailTemplate.html,
      emailTemplate.inlinedAttachments,
    );

    this.logger.log(
      `[EmailService] - Sent activation email in language '${language}' to: ${email}`,
    );
  }

  /**
   * Determines if an email sending error should be retried
   */
  isRetryableEmailError(error: any): boolean {
    if (!error) return false;

    const errorMessage = error.message?.toLowerCase() || '';
    const errorCode = error.code;

    // Network-related errors that should be retried
    const networkErrors = [
      'timeout',
      'network',
      'econnreset',
      'enotfound',
      'econnrefused',
      'etimedout',
      'socket hang up',
      'connection reset',
    ];

    // Mail service specific errors that should be retried
    const mailServiceErrors = [
      'mail sending failed',
      'temporary failure',
      'service unavailable',
      'rate limit',
      'too many requests',
    ];

    // Check error message
    const hasRetryableMessage = [...networkErrors, ...mailServiceErrors].some(
      (errorType) => errorMessage.includes(errorType),
    );

    // Check error codes
    const retryableCodes = [
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'EPIPE',
    ];
    const hasRetryableCode = retryableCodes.includes(errorCode);

    // Check error names
    const retryableNames = ['AbortError', 'TypeError', 'NetworkError'];
    const hasRetryableName = retryableNames.includes(error.name);

    return hasRetryableMessage || hasRetryableCode || hasRetryableName;
  }
}
