import { Test, TestingModule } from '@nestjs/testing';
import { PurchasesService } from './purchases.service';
import { ShopifyOrder } from './interfaces/shopify.interface';
import { DatabaseService } from '../database/database.service';
import { RedisService } from '../redis/redis.service';
import { FirebaseService } from '../firebase/firebase.service';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('PurchasesService', () => {
  let service: PurchasesService;
  let mockDatabase: any;
  let mockRedisService: any;
  let mockFirebaseService: any;

  beforeEach(async () => {
    mockDatabase = {
      purchase_additional_info: {
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        findMany: jest.fn(),
      },
      purchase: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        count: jest.fn(),
      },
    };

    mockRedisService = {
      get: jest.fn(),
      set: jest.fn(),
      getHealth: jest.fn(),
      deleteByPattern: jest.fn(),
    };

    mockFirebaseService = {
      getFirestore: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PurchasesService,
        {
          provide: DatabaseService,
          useValue: mockDatabase,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
      ],
    }).compile();

    service = module.get<PurchasesService>(PurchasesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Shopify Integration', () => {
    it('should handle Shopify order data structure', () => {
      const shopifyOrder: ShopifyOrder = {
        id: '12345',
        order_number: '1001',
        name: '#1001',
        email: '<EMAIL>',
        financial_status: 'paid',
        fulfillment_status: 'unfulfilled',
        total_price: '99.99',
        currency: 'USD',
        customer: {
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          phone: '+**********',
        },
        billing_address: {
          address1: '123 Main St',
          address2: 'Apt 4B',
          city: 'New York',
          province: 'NY',
          zip: '10001',
          country: 'USA',
          phone: '+**********',
        },
        line_items: [
          {
            title: 'VR Training License',
            quantity: 2,
            price: '49.99',
            sku: 'VR-LICENSE-001',
          },
        ],
      };

      expect(shopifyOrder).toBeDefined();
      expect(shopifyOrder.customer?.first_name).toBe('John');
      expect(shopifyOrder.line_items?.length).toBe(1);
    });

    it('should convert Shopify order to CreatePurchaseDto', () => {
      const mockService = {
        convertShopifyOrderToPurchase: jest.fn().mockReturnValue({
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          code: '1001',
          numberOfVrGlasses: 0,
          numberOfLicenses: 2,
          isSubscription: false,
          duration: 365,
          orderNumber: '1001',
        }),
      };

      const shopifyOrder: ShopifyOrder = {
        id: '12345',
        order_number: '1001',
        customer: {
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
        },
      };

      const result = mockService.convertShopifyOrderToPurchase(shopifyOrder);
      expect(result).toBeDefined();
      expect(result.email).toBe('<EMAIL>');
      expect(result.firstName).toBe('John');
      expect(result.lastName).toBe('Doe');
    });
  });

  describe('Activation Code Notification Status', () => {
    it('should validate notification status structure', () => {
      const validStatus = {
        activationCodeEmailStatus: 'sent',
        activationCodeSmsStatus: 'failed',
      };

      expect(validStatus).toHaveProperty('activationCodeEmailStatus');
      expect(validStatus).toHaveProperty('activationCodeSmsStatus');
      expect(validStatus.activationCodeEmailStatus).toBe('sent');
      expect(validStatus.activationCodeSmsStatus).toBe('failed');
    });

    it('should handle different notification status values', () => {
      const statusValues = ['pending', 'sent', 'failed', 'no_phone'];

      statusValues.forEach((status) => {
        const notificationStatus = {
          activationCodeEmailStatus: status,
          activationCodeSmsStatus: status,
        };

        expect(typeof notificationStatus.activationCodeEmailStatus).toBe(
          'string',
        );
        expect(typeof notificationStatus.activationCodeSmsStatus).toBe(
          'string',
        );
      });
    });

    it('should allow partial status updates', () => {
      const emailOnlyStatus = {
        activationCodeEmailStatus: 'sent',
      };

      const smsOnlyStatus = {
        activationCodeSmsStatus: 'failed',
      };

      expect(emailOnlyStatus).toHaveProperty('activationCodeEmailStatus');
      expect(emailOnlyStatus).not.toHaveProperty('activationCodeSmsStatus');

      expect(smsOnlyStatus).toHaveProperty('activationCodeSmsStatus');
      expect(smsOnlyStatus).not.toHaveProperty('activationCodeEmailStatus');
    });
  });

  describe('Redis Fallback Logic', () => {
    const mockPurchases = [
      {
        id: 1,
        email: '<EMAIL>',
        created_at: new Date(),
        activations: [],
        additional_info: null,
      },
    ];

    beforeEach(() => {
      mockDatabase.purchase.findMany.mockResolvedValue(mockPurchases);
    });

    describe('getPurchases', () => {
      it('should return cached data when Redis is available', async () => {
        const cachedData = JSON.stringify(mockPurchases);
        mockRedisService.getHealth.mockResolvedValue(true);
        mockRedisService.get.mockResolvedValue(cachedData);

        const result = await service.getPurchases(1, 10);

        expect(mockRedisService.get).toHaveBeenCalled();
        expect(result).toEqual(mockPurchases);
        expect(mockDatabase.purchase.findMany).not.toHaveBeenCalled();
      });

      it('should fallback to database when Redis is unavailable', async () => {
        mockRedisService.getHealth.mockResolvedValue(false);
        mockRedisService.get.mockResolvedValue(null);

        const result = await service.getPurchases(1, 10);

        expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
        expect(result).toEqual(mockPurchases);
      });

      it('should fallback to database when Redis throws error', async () => {
        mockRedisService.getHealth.mockResolvedValue(true);
        mockRedisService.get.mockRejectedValue(new Error('Redis connection failed'));

        const result = await service.getPurchases(1, 10);

        expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
        expect(result).toEqual(mockPurchases);
      });

      it('should continue without caching when Redis set fails', async () => {
        mockRedisService.getHealth.mockResolvedValue(false);
        mockRedisService.get.mockResolvedValue(null);
        mockRedisService.set.mockRejectedValue(new Error('Redis set failed'));

        const result = await service.getPurchases(1, 10);

        expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
        expect(result).toEqual(mockPurchases);
        // Should not throw error even if caching fails
      });
    });

    describe('getPurchaseStatusesByDateRange', () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const mockStatusesMap = {
        1: {
          purchaseId: 1,
          purchaseDate: new Date(),
          orderStatus: null,
          shippingInfo: null,
          additionalInfo: null,
          activationRecords: [],
          validUntil: null,
        },
      };

      beforeEach(() => {
        mockDatabase.purchase.findMany.mockResolvedValue([
          {
            id: 1,
            created_at: new Date(),
            activations: [],
            orderStatus: null,
            shipping_Info: null,
            additional_info: null,
          },
        ]);
      });

      it('should return cached data when Redis is available', async () => {
        const cachedData = JSON.stringify(mockStatusesMap);
        mockRedisService.getHealth.mockResolvedValue(true);
        mockRedisService.get.mockResolvedValue(cachedData);

        const result = await service.getPurchaseStatusesByDateRange(startDate, endDate);

        expect(mockRedisService.get).toHaveBeenCalled();
        expect(result).toEqual(mockStatusesMap);
        expect(mockDatabase.purchase.findMany).not.toHaveBeenCalled();
      });

      it('should fallback to database when Redis is unavailable', async () => {
        mockRedisService.getHealth.mockResolvedValue(false);
        mockRedisService.get.mockResolvedValue(null);

        const result = await service.getPurchaseStatusesByDateRange(startDate, endDate);

        expect(mockDatabase.purchase.findMany).toHaveBeenCalledWith({
          where: {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          },
          include: expect.any(Object),
        });
        expect(result).toBeDefined();
        expect(result[1]).toBeDefined();
      });
    });

    describe('Cache invalidation', () => {
      it('should skip cache invalidation when Redis is unavailable', async () => {
        mockRedisService.getHealth.mockResolvedValue(false);

        // This should not throw an error
        await service['invalidatePurchasesCaches']();

        expect(mockRedisService.deleteByPattern).not.toHaveBeenCalled();
      });

      it('should continue when cache invalidation fails', async () => {
        mockRedisService.getHealth.mockResolvedValue(true);
        mockRedisService.deleteByPattern.mockRejectedValue(new Error('Redis delete failed'));

        // This should not throw an error
        await service['invalidatePurchasesCaches']();

        expect(mockRedisService.deleteByPattern).toHaveBeenCalled();
      });
    });
  });
});
