import { Test, TestingModule } from '@nestjs/testing';
import { PurchasesService } from './purchases.service';
import { DatabaseService } from '../database/database.service';
import { RedisService } from '../redis/redis.service';
import { FirebaseService } from '../firebase/firebase.service';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('PurchasesService - Redis Fallback', () => {
  let service: PurchasesService;
  let mockDatabase: any;
  let mockRedisService: any;
  let mockFirebaseService: any;

  const mockPurchases = [
    {
      id: 1,
      email: '<EMAIL>',
      created_at: new Date(),
      activations: [],
      additional_info: null,
    },
  ];

  beforeEach(async () => {
    mockDatabase = {
      purchase: {
        findMany: jest.fn().mockResolvedValue(mockPurchases),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        count: jest.fn(),
      },
      purchase_additional_info: {
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        findMany: jest.fn(),
      },
    };

    mockRedisService = {
      get: jest.fn(),
      set: jest.fn(),
      getHealth: jest.fn(),
      deleteByPattern: jest.fn(),
    };

    mockFirebaseService = {
      getFirestore: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PurchasesService,
        {
          provide: DatabaseService,
          useValue: mockDatabase,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
      ],
    }).compile();

    service = module.get<PurchasesService>(PurchasesService);
  });

  describe('Redis Health Check and Fallback', () => {
    it('should return cached data when Redis is healthy and has data', async () => {
      const cachedData = JSON.stringify(mockPurchases);
      mockRedisService.getHealth.mockResolvedValue(true);
      mockRedisService.get.mockResolvedValue(cachedData);

      const result = await service.getPurchases(1, 10);

      expect(mockRedisService.getHealth).toHaveBeenCalled();
      expect(mockRedisService.get).toHaveBeenCalled();
      // When data is cached and retrieved, dates become strings
      expect(result).toEqual(JSON.parse(cachedData));
      expect(mockDatabase.purchase.findMany).not.toHaveBeenCalled();
    });

    it('should fallback to database when Redis is unhealthy', async () => {
      mockRedisService.getHealth.mockResolvedValue(false);
      mockRedisService.get.mockResolvedValue(null);

      const result = await service.getPurchases(1, 10);

      expect(mockRedisService.getHealth).toHaveBeenCalled();
      expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockPurchases);
    });

    it('should fallback to database when Redis get throws error', async () => {
      mockRedisService.getHealth.mockResolvedValue(true);
      mockRedisService.get.mockRejectedValue(new Error('Redis connection failed'));

      const result = await service.getPurchases(1, 10);

      expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockPurchases);
    });

    it('should continue without caching when Redis set fails', async () => {
      mockRedisService.getHealth.mockResolvedValue(false);
      mockRedisService.get.mockResolvedValue(null);
      mockRedisService.set.mockRejectedValue(new Error('Redis set failed'));

      const result = await service.getPurchases(1, 10);

      expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockPurchases);
      // Should not throw error even if caching fails
    });

    it('should skip cache invalidation when Redis is unavailable', async () => {
      mockRedisService.getHealth.mockResolvedValue(false);

      // Access private method for testing
      await (service as any).invalidatePurchasesCaches();

      expect(mockRedisService.getHealth).toHaveBeenCalled();
      expect(mockRedisService.deleteByPattern).not.toHaveBeenCalled();
    });

    it('should continue when cache invalidation fails', async () => {
      mockRedisService.getHealth.mockResolvedValue(true);
      mockRedisService.deleteByPattern.mockRejectedValue(new Error('Redis delete failed'));

      // Access private method for testing
      await (service as any).invalidatePurchasesCaches();

      expect(mockRedisService.getHealth).toHaveBeenCalled();
      expect(mockRedisService.deleteByPattern).toHaveBeenCalled();
      // Should not throw error
    });
  });

  describe('getPurchaseStatusesByDateRange - Redis Fallback', () => {
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-31');
    const mockPurchaseWithRelations = [
      {
        id: 1,
        created_at: new Date(),
        activations: [],
        orderStatus: null,
        shipping_Info: null,
        additional_info: null,
      },
    ];

    beforeEach(() => {
      mockDatabase.purchase.findMany.mockResolvedValue(mockPurchaseWithRelations);
    });

    it('should return cached data when Redis is available', async () => {
      const mockStatusesMap = {
        1: {
          purchaseId: 1,
          purchaseDate: new Date(),
          orderStatus: null,
          shippingInfo: null,
          additionalInfo: null,
          activationRecords: [],
          validUntil: null,
        },
      };
      const cachedData = JSON.stringify(mockStatusesMap);
      mockRedisService.getHealth.mockResolvedValue(true);
      mockRedisService.get.mockResolvedValue(cachedData);

      const result = await service.getPurchaseStatusesByDateRange(startDate, endDate);

      expect(mockRedisService.get).toHaveBeenCalled();
      // When data is cached and retrieved, dates become strings
      expect(result).toEqual(JSON.parse(cachedData));
      expect(mockDatabase.purchase.findMany).not.toHaveBeenCalled();
    });

    it('should fallback to database when Redis is unavailable', async () => {
      mockRedisService.getHealth.mockResolvedValue(false);
      mockRedisService.get.mockResolvedValue(null);

      const result = await service.getPurchaseStatusesByDateRange(startDate, endDate);

      expect(mockDatabase.purchase.findMany).toHaveBeenCalledWith({
        where: {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: expect.any(Object),
      });
      expect(result).toBeDefined();
      expect(result[1]).toBeDefined();
    });
  });

  describe('Safe Redis Methods', () => {
    it('should return null when Redis health check fails in safeRedisGet', async () => {
      mockRedisService.getHealth.mockResolvedValue(false);

      const result = await (service as any).safeRedisGet('test-key');

      expect(result).toBeNull();
      expect(mockRedisService.get).not.toHaveBeenCalled();
    });

    it('should return null when Redis get throws error in safeRedisGet', async () => {
      mockRedisService.getHealth.mockResolvedValue(true);
      mockRedisService.get.mockRejectedValue(new Error('Redis error'));

      const result = await (service as any).safeRedisGet('test-key');

      expect(result).toBeNull();
    });

    it('should skip set when Redis health check fails in safeRedisSet', async () => {
      mockRedisService.getHealth.mockResolvedValue(false);

      await (service as any).safeRedisSet('test-key', 'test-value', 3600);

      expect(mockRedisService.set).not.toHaveBeenCalled();
    });

    it('should continue when Redis set throws error in safeRedisSet', async () => {
      mockRedisService.getHealth.mockResolvedValue(true);
      mockRedisService.set.mockRejectedValue(new Error('Redis set error'));

      // Should not throw
      await (service as any).safeRedisSet('test-key', 'test-value', 3600);

      expect(mockRedisService.set).toHaveBeenCalled();
    });
  });
});
