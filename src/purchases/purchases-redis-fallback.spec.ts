import { Test, TestingModule } from '@nestjs/testing';
import { PurchasesService } from './purchases.service';
import { DatabaseService } from '../database/database.service';
import { RedisService } from '../redis/redis.service';
import { FirebaseService } from '../firebase/firebase.service';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('PurchasesService - Redis Fallback', () => {
  let service: PurchasesService;
  let mockDatabase: any;
  let mockRedisService: any;
  let mockFirebaseService: any;

  const mockPurchases = [
    {
      id: 1,
      email: '<EMAIL>',
      created_at: new Date(),
      activations: [],
      additional_info: null,
    },
  ];

  beforeEach(async () => {
    mockDatabase = {
      purchase: {
        findMany: jest.fn().mockResolvedValue(mockPurchases),
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        count: jest.fn(),
      },
      purchase_additional_info: {
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        findMany: jest.fn(),
      },
    };

    mockRedisService = {
      get: jest.fn(),
      set: jest.fn(),
      getHealth: jest.fn(),
      deleteByPattern: jest.fn(),
      safeGet: jest.fn(),
      safeSet: jest.fn(),
      safeDeleteByPattern: jest.fn(),
    };

    mockFirebaseService = {
      getFirestore: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PurchasesService,
        {
          provide: DatabaseService,
          useValue: mockDatabase,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
      ],
    }).compile();

    service = module.get<PurchasesService>(PurchasesService);
  });

  describe('Redis Health Check and Fallback', () => {
    it('should return cached data when Redis is healthy and has data', async () => {
      const cachedData = JSON.stringify(mockPurchases);
      mockRedisService.safeGet.mockResolvedValue(cachedData);

      const result = await service.getPurchases(1, 10);

      expect(mockRedisService.safeGet).toHaveBeenCalled();
      // When data is cached and retrieved, dates become strings
      expect(result).toEqual(JSON.parse(cachedData));
      expect(mockDatabase.purchase.findMany).not.toHaveBeenCalled();
    });

    it('should fallback to database when Redis is unhealthy', async () => {
      mockRedisService.safeGet.mockResolvedValue(null);

      const result = await service.getPurchases(1, 10);

      expect(mockRedisService.safeGet).toHaveBeenCalled();
      expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockPurchases);
    });

    it('should fallback to database when Redis get throws error', async () => {
      mockRedisService.safeGet.mockResolvedValue(null);

      const result = await service.getPurchases(1, 10);

      expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockPurchases);
    });

    it('should continue without caching when Redis set fails', async () => {
      mockRedisService.safeGet.mockResolvedValue(null);
      mockRedisService.safeSet.mockResolvedValue(undefined);

      const result = await service.getPurchases(1, 10);

      expect(mockDatabase.purchase.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockPurchases);
      // Should not throw error even if caching fails
    });

    it('should skip cache invalidation when Redis is unavailable', async () => {
      mockRedisService.safeDeleteByPattern.mockResolvedValue(0);

      // Access private method for testing
      await (service as any).invalidatePurchasesCaches();

      expect(mockRedisService.safeDeleteByPattern).toHaveBeenCalled();
    });

    it('should continue when cache invalidation fails', async () => {
      mockRedisService.safeDeleteByPattern.mockResolvedValue(0);

      // Access private method for testing
      await (service as any).invalidatePurchasesCaches();

      expect(mockRedisService.safeDeleteByPattern).toHaveBeenCalled();
      // Should not throw error
    });
  });

  describe('getPurchaseStatusesByDateRange - Redis Fallback', () => {
    const startDate = new Date('2024-01-01');
    const endDate = new Date('2024-01-31');
    const mockPurchaseWithRelations = [
      {
        id: 1,
        created_at: new Date(),
        activations: [],
        orderStatus: null,
        shipping_Info: null,
        additional_info: null,
      },
    ];

    beforeEach(() => {
      mockDatabase.purchase.findMany.mockResolvedValue(mockPurchaseWithRelations);
    });

    it('should return cached data when Redis is available', async () => {
      const mockStatusesMap = {
        1: {
          purchaseId: 1,
          purchaseDate: new Date(),
          orderStatus: null,
          shippingInfo: null,
          additionalInfo: null,
          activationRecords: [],
          validUntil: null,
        },
      };
      const cachedData = JSON.stringify(mockStatusesMap);
      mockRedisService.safeGet.mockResolvedValue(cachedData);

      const result = await service.getPurchaseStatusesByDateRange(startDate, endDate);

      expect(mockRedisService.safeGet).toHaveBeenCalled();
      // When data is cached and retrieved, dates become strings
      expect(result).toEqual(JSON.parse(cachedData));
      expect(mockDatabase.purchase.findMany).not.toHaveBeenCalled();
    });

    it('should fallback to database when Redis is unavailable', async () => {
      mockRedisService.safeGet.mockResolvedValue(null);

      const result = await service.getPurchaseStatusesByDateRange(startDate, endDate);

      expect(mockDatabase.purchase.findMany).toHaveBeenCalledWith({
        where: {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: expect.any(Object),
      });
      expect(result).toBeDefined();
      expect(result[1]).toBeDefined();
    });
  });

  describe('Redis Service Integration', () => {
    it('should use RedisService safeGet method', async () => {
      mockRedisService.safeGet.mockResolvedValue(null);

      await service.getPurchases(1, 10);

      expect(mockRedisService.safeGet).toHaveBeenCalled();
    });

    it('should use RedisService safeSet method', async () => {
      mockRedisService.safeGet.mockResolvedValue(null);
      mockRedisService.safeSet.mockResolvedValue(undefined);

      await service.getPurchases(1, 10);

      expect(mockRedisService.safeSet).toHaveBeenCalled();
    });

    it('should use RedisService safeDeleteByPattern method', async () => {
      mockRedisService.safeDeleteByPattern.mockResolvedValue(0);

      await (service as any).invalidatePurchasesCaches();

      expect(mockRedisService.safeDeleteByPattern).toHaveBeenCalled();
    });
  });
});
