import { Injectable, Logger } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisService {
  private readonly redis: Redis;
  private readonly logger = new Logger(RedisService.name);
  private readonly SCAN_COUNT = 100;

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        this.logger.log(`Retrying Redis connection in ${delay}ms...`);
        return delay;
      },
    });

    this.redis.on('connect', () => {
      this.logger.log('Successfully connected to Redis');
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis connection error:', error);
    });
  }

  async get(key: string): Promise<string | null> {
    try {
      return await this.redis.get(key);
    } catch (error) {
      this.logger.error(`Redis GET failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async set(key: string, value: string, expireSeconds?: number): Promise<void> {
    try {
      if (expireSeconds) {
        await this.redis.setex(key, expireSeconds, value);
      } else {
        await this.redis.set(key, value);
      }
    } catch (error) {
      this.logger.error(`Redis SET failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async del(key: string): Promise<void> {
    await this.redis.del(key);
  }

  async keys(pattern: string): Promise<string[]> {
    const methodName = 'keys';
    try {
      const keys: string[] = [];
      let cursor = '0';

      do {
        const [nextCursor, batch] = await this.redis.scan(
          cursor,
          'MATCH',
          pattern,
          'COUNT',
          this.SCAN_COUNT,
        );

        // Update cursor for next iteration
        cursor = nextCursor;

        // Add found keys to our array
        if (batch.length > 0) {
          keys.push(...batch);
        }

        this.logger.debug(
          `${methodName}: Scanned batch with cursor ${cursor}, found ${batch.length} keys`,
        );
      } while (cursor !== '0'); // Continue until cursor returns to 0

      this.logger.log(
        `${methodName}: Successfully found ${keys.length} keys matching pattern: ${pattern}`,
      );

      return keys;
    } catch (error) {
      this.logger.error(
        `${methodName}: Failed to scan Redis keys with pattern ${pattern}: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  async deleteByPattern(pattern: string): Promise<number> {
    const methodName = 'deleteByPattern';
    try {
      let totalDeleted = 0;
      let cursor = '0';

      do {
        // Scan for matching keys
        const [nextCursor, batch] = await this.redis.scan(
          cursor,
          'MATCH',
          pattern,
          'COUNT',
          this.SCAN_COUNT,
        );

        cursor = nextCursor;

        // Delete found keys in batch
        if (batch.length > 0) {
          const deleted = await this.redis.del(...batch);
          totalDeleted += deleted;

          this.logger.debug(
            `${methodName}: Deleted ${deleted} keys from batch of ${batch.length}`,
          );
        }
      } while (cursor !== '0');

      this.logger.log(
        `${methodName}: Successfully deleted ${totalDeleted} keys matching pattern: ${pattern}`,
      );

      return totalDeleted;
    } catch (error) {
      this.logger.error(
        `${methodName}: Failed to delete keys with pattern ${pattern}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  async getHealth(): Promise<boolean> {
    try {
      // Check connection status first
      if (this.redis.status !== 'ready') {
        this.logger.warn(`Redis connection status: ${this.redis.status}`);
        return false;
      }

      // Perform ping test with timeout
      const pingPromise = this.redis.ping();
      const timeoutPromise = new Promise<string>((_, reject) => {
        setTimeout(() => reject(new Error('Ping timeout')), 5000);
      });

      const ping = await Promise.race([pingPromise, timeoutPromise]);
      return ping === 'PONG';
    } catch (error) {
      this.logger.error('Redis health check failed:', error.message);
      return false;
    }
  }

  /**
   * Check if Redis is currently connected and ready
   */
  isConnected(): boolean {
    return this.redis.status === 'ready';
  }

  async getMemoryInfo(): Promise<any> {
    try {
      const info = await this.redis.info('memory');
      return info;
    } catch (error) {
      this.logger.error('Failed to get Redis memory info:', error);
      throw error;
    }
  }

  /**
   * Safe Redis get with automatic fallback
   * Returns null if Redis is unavailable or operation fails
   */
  async safeGet(key: string): Promise<string | null> {
    try {
      const isAvailable = await this.getHealth();
      if (!isAvailable) {
        this.logger.warn(`Redis unavailable, skipping cache get for key: ${key}`);
        return null;
      }
      return await this.get(key);
    } catch (error) {
      this.logger.warn(`Redis get failed for key ${key}: ${error.message}, falling back`);
      return null;
    }
  }

  /**
   * Safe Redis set with automatic fallback
   * Continues silently if Redis is unavailable or operation fails
   */
  async safeSet(key: string, value: string, ttl?: number): Promise<void> {
    try {
      const isAvailable = await this.getHealth();
      if (!isAvailable) {
        this.logger.warn(`Redis unavailable, skipping cache set for key: ${key}`);
        return;
      }
      await this.set(key, value, ttl);
      this.logger.debug(`Successfully cached data for key: ${key}`);
    } catch (error) {
      this.logger.warn(`Redis set failed for key ${key}: ${error.message}, continuing without cache`);
    }
  }

  /**
   * Safe Redis delete by pattern with automatic fallback
   * Returns 0 if Redis is unavailable or operation fails
   */
  async safeDeleteByPattern(pattern: string): Promise<number> {
    try {
      const isAvailable = await this.getHealth();
      if (!isAvailable) {
        this.logger.warn(`Redis unavailable, skipping cache invalidation for pattern: ${pattern}`);
        return 0;
      }
      return await this.deleteByPattern(pattern);
    } catch (error) {
      this.logger.warn(`Redis delete failed for pattern ${pattern}: ${error.message}, continuing without cache invalidation`);
      return 0;
    }
  }
}
