import { Test, TestingModule } from '@nestjs/testing';
import { RedisService } from './redis.service';
import { Logger } from '@nestjs/common';

// Create a mock Redis instance
const mockRedisInstance = {
  get: jest.fn(),
  set: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  scan: jest.fn(),
  ping: jest.fn(),
  status: 'ready',
  on: jest.fn(),
  info: jest.fn(),
};

// Mock ioredis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => mockRedisInstance);
});

describe('RedisService - Safe Methods', () => {
  let service: RedisService;
  let mockRedis: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RedisService],
    }).compile();

    service = module.get<RedisService>(RedisService);
    // Use the mock Redis instance
    mockRedis = mockRedisInstance;
    
    // Mock logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('safeGet', () => {
    it('should return data when Redis is healthy and get succeeds', async () => {
      const testValue = 'test-value';
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.get.mockResolvedValue(testValue);

      const result = await service.safeGet('test-key');

      expect(result).toBe(testValue);
      expect(mockRedis.ping).toHaveBeenCalled();
      expect(mockRedis.get).toHaveBeenCalledWith('test-key');
    });

    it('should return null when Redis health check fails', async () => {
      mockRedis.status = 'connecting';
      
      const result = await service.safeGet('test-key');

      expect(result).toBeNull();
      expect(mockRedis.get).not.toHaveBeenCalled();
    });

    it('should return null when Redis get throws error', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.get.mockRejectedValue(new Error('Redis get error'));

      const result = await service.safeGet('test-key');

      expect(result).toBeNull();
      expect(mockRedis.get).toHaveBeenCalledWith('test-key');
    });

    it('should return null when ping times out', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockImplementation(() => new Promise(() => {})); // Never resolves

      const result = await service.safeGet('test-key');

      expect(result).toBeNull();
      expect(mockRedis.get).not.toHaveBeenCalled();
    });
  });

  describe('safeSet', () => {
    it('should set data when Redis is healthy', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.setex.mockResolvedValue('OK');

      await service.safeSet('test-key', 'test-value', 3600);

      expect(mockRedis.ping).toHaveBeenCalled();
      expect(mockRedis.setex).toHaveBeenCalledWith('test-key', 3600, 'test-value');
    });

    it('should set data without TTL when no expiration provided', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.set.mockResolvedValue('OK');

      await service.safeSet('test-key', 'test-value');

      expect(mockRedis.ping).toHaveBeenCalled();
      expect(mockRedis.set).toHaveBeenCalledWith('test-key', 'test-value');
    });

    it('should skip set when Redis health check fails', async () => {
      mockRedis.status = 'connecting';

      await service.safeSet('test-key', 'test-value', 3600);

      expect(mockRedis.set).not.toHaveBeenCalled();
      expect(mockRedis.setex).not.toHaveBeenCalled();
    });

    it('should continue silently when Redis set throws error', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.setex.mockRejectedValue(new Error('Redis set error'));

      // Should not throw
      await expect(service.safeSet('test-key', 'test-value', 3600)).resolves.toBeUndefined();

      expect(mockRedis.setex).toHaveBeenCalledWith('test-key', 3600, 'test-value');
    });
  });

  describe('safeDeleteByPattern', () => {
    it('should delete keys when Redis is healthy', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.scan
        .mockResolvedValueOnce(['0', ['key1', 'key2']])
        .mockResolvedValueOnce(['0', []]);
      mockRedis.del.mockResolvedValue(2);

      const result = await service.safeDeleteByPattern('test:*');

      expect(result).toBe(2);
      expect(mockRedis.ping).toHaveBeenCalled();
      expect(mockRedis.scan).toHaveBeenCalled();
      expect(mockRedis.del).toHaveBeenCalledWith('key1', 'key2');
    });

    it('should return 0 when Redis health check fails', async () => {
      mockRedis.status = 'connecting';

      const result = await service.safeDeleteByPattern('test:*');

      expect(result).toBe(0);
      expect(mockRedis.scan).not.toHaveBeenCalled();
      expect(mockRedis.del).not.toHaveBeenCalled();
    });

    it('should return 0 when Redis delete throws error', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.scan.mockRejectedValue(new Error('Redis scan error'));

      const result = await service.safeDeleteByPattern('test:*');

      expect(result).toBe(0);
      expect(mockRedis.scan).toHaveBeenCalled();
    });

    it('should handle multiple scan iterations', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');
      mockRedis.scan
        .mockResolvedValueOnce(['10', ['key1', 'key2']])
        .mockResolvedValueOnce(['0', ['key3']]);
      mockRedis.del
        .mockResolvedValueOnce(2)
        .mockResolvedValueOnce(1);

      const result = await service.safeDeleteByPattern('test:*');

      expect(result).toBe(3);
      expect(mockRedis.scan).toHaveBeenCalledTimes(2);
      expect(mockRedis.del).toHaveBeenCalledTimes(2);
      expect(mockRedis.del).toHaveBeenNthCalledWith(1, 'key1', 'key2');
      expect(mockRedis.del).toHaveBeenNthCalledWith(2, 'key3');
    });
  });

  describe('getHealth', () => {
    it('should return true when Redis is ready and ping succeeds', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockResolvedValue('PONG');

      const result = await service.getHealth();

      expect(result).toBe(true);
      expect(mockRedis.ping).toHaveBeenCalled();
    });

    it('should return false when Redis status is not ready', async () => {
      mockRedis.status = 'connecting';

      const result = await service.getHealth();

      expect(result).toBe(false);
      expect(mockRedis.ping).not.toHaveBeenCalled();
    });

    it('should return false when ping fails', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockRejectedValue(new Error('Ping failed'));

      const result = await service.getHealth();

      expect(result).toBe(false);
      expect(mockRedis.ping).toHaveBeenCalled();
    });

    it('should return false when ping times out', async () => {
      mockRedis.status = 'ready';
      mockRedis.ping.mockImplementation(() => new Promise(() => {})); // Never resolves

      const result = await service.getHealth();

      expect(result).toBe(false);
      expect(mockRedis.ping).toHaveBeenCalled();
    });
  });
});
