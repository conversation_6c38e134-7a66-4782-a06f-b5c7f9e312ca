/*
  Warnings:

  - The values [EDITOR] on the enum `admin_user_role` will be removed. If these variants are still used in the database, this will fail.
  - The values [EDITOR] on the enum `admin_user_role` will be removed. If these variants are still used in the database, this will fail.
  - A unique constraint covering the columns `[title,message,type,metadata]` on the table `admin_notifications` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `admin_accounts` MODIFY `role` ENUM('SUPERADMIN', 'ADMIN', 'SUBADMIN', 'TEACHER', 'USER', 'MARKETING', 'DEVELOPER') NOT NULL DEFAULT 'USER';

-- AlterTable
ALTER TABLE `admin_todos` ADD COLUMN `assigned_to_name` VARCHAR(255) NULL,
    ADD COLUMN `created_by_name` VARCHAR(255) NULL,
    ADD COLUMN `is_private` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `updated_by` VARCHAR(191) NULL,
    ADD COLUMN `updated_by_name` VARCHAR(255) NULL;

-- AlterTable
ALTER TABLE `admin_user` MODIFY `role` ENUM('SUPERADMIN', 'ADMIN', 'SUBADMIN', 'TEACHER', 'USER', 'MARKETING', 'DEVELOPER') NOT NULL DEFAULT 'USER';

-- AlterTable
ALTER TABLE `purchase_additional_info` ADD COLUMN `activation_code_notification_status` JSON NULL,
    ADD COLUMN `shipped` BOOLEAN NULL DEFAULT false;

-- AlterTable
ALTER TABLE `user_correct_answer_times` ADD COLUMN `click_positions` JSON NULL;

-- AlterTable
ALTER TABLE `user_wrong_answer_times` ADD COLUMN `click_positions` JSON NULL;

-- CreateTable
CREATE TABLE `training_list` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `is_public` BOOLEAN NOT NULL DEFAULT false,
    `is_active` BOOLEAN NOT NULL DEFAULT false,
    `teacher_id` INTEGER NOT NULL,
    `school_class_id` INTEGER NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `training_item` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `training_list_id` INTEGER NOT NULL,
    `type` ENUM('image', 'video', 'binogi', 'online') NOT NULL,
    `name` VARCHAR(191) NULL,
    `url` VARCHAR(191) NULL,
    `order_no` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `duration_seconds` INTEGER NOT NULL DEFAULT 0,

    INDEX `training_item_training_list_id_order_no_idx`(`training_list_id`, `order_no`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `admin_notifications_title_message_type_metadata_key` ON `admin_notifications`(`title`, `message`, `type`, `metadata`);

-- CreateIndex
CREATE INDEX `user_correct_answer_times_chain_id_idx` ON `user_correct_answer_times`(`chain_id`);

-- CreateIndex
CREATE INDEX `user_wrong_answer_times_chain_id_idx` ON `user_wrong_answer_times`(`chain_id`);

-- AddForeignKey
ALTER TABLE `training_list` ADD CONSTRAINT `training_list_teacher_id_fkey` FOREIGN KEY (`teacher_id`) REFERENCES `teacher`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `training_list` ADD CONSTRAINT `training_list_school_class_id_fkey` FOREIGN KEY (`school_class_id`) REFERENCES `school_class`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `training_item` ADD CONSTRAINT `training_item_training_list_id_fkey` FOREIGN KEY (`training_list_id`) REFERENCES `training_list`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
